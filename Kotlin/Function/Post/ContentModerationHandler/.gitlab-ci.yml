Kotlin/Function/Post/ContentModerationHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/Post/ContentModerationHandler/variables:
  variables:
    FUNCTION_NAME: "content-moderation-handler"
    CLASS_NAME: "hero.functions.ContentModerationHandler"
    TOPIC: "PostStateChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/Post/ContentModerationHandler/deploy-devel:
  needs:
    - Kotlin/Function/Post/ContentModerationHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/Post/ContentModerationHandler/variables

Kotlin/Function/Post/ContentModerationHandler/deploy-staging:
  needs:
    - Kotlin/Function/Post/ContentModerationHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/Post/ContentModerationHandler/variables

Kotlin/Function/Post/ContentModerationHandler/deploy-prod:
  needs:
    - Kotlin/Function/Post/ContentModerationHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/Post/ContentModerationHandler/variables
