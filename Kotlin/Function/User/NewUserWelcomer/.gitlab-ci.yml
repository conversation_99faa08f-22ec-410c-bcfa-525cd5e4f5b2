Kotlin/Function/User/NewUserWelcomer/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/User/NewUserWelcomer/variables:
  variables:
    FUNCTION_NAME: "new-user-welcomer"
    CLASS_NAME: "hero.functions.NewUserWelcomer"
    TOPIC: "UserStateChanged"
    SERVICE_ACCOUNT: "<EMAIL>"

Kotlin/Function/User/NewUserWelcomer/deploy-devel:
  needs:
    - Kotlin/Function/User/NewUserWelcomer/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/User/NewUserWelcomer/variables

Kotlin/Function/User/NewUserWelcomer/deploy-staging:
  needs:
    - Kotlin/Function/User/NewUserWelcomer/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/User/NewUserWelcomer/variables

Kotlin/Function/User/NewUserWelcomer/deploy-prod:
  needs:
    - Kotlin/Function/User/NewUserWelcomer/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/User/NewUserWelcomer/variables
