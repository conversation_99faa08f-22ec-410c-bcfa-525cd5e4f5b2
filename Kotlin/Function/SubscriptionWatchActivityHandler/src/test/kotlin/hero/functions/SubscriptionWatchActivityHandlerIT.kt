package hero.functions

import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.jooq.Tables.WATCH_ACTIVITY
import hero.test.IntegrationTest
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SubscriptionWatchActivityHandlerIT : IntegrationTest() {
    @Test
    fun `should update watch activity subscription status to true`() {
        val underTest = SubscriptionWatchActivityHandler(lazyTestContext, TestEnvironmentVariables, TestLogger)

        testHelper.createUser("cestmir")
        testHelper.createUser("jonas")
        val post = testHelper.createPost(userId = "cestmir")
        testHelper.createWatchActivity(
            post = post,
            assetId = "asset-id",
            "jonas",
            subscriptionActive = false,
        )

        underTest.consume(
            SubscriberStatusChanged(
                userId = "jonas",
                creatorId = "cestmir",
                statusChange = SubscriberStatusChange.SUBSCRIBED,
                refunded = false,
                refused = false,
                cancelledReason = "",
                ended = false,
                doNotNotify = false,
            ),
        )

        with(testContext.selectFrom(WATCH_ACTIVITY).fetchSingle()) {
            assertThat(this[WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE]).isTrue()
        }
    }

    @Test
    fun `should update watch activity subscription status to false`() {
        val underTest = SubscriptionWatchActivityHandler(lazyTestContext, TestEnvironmentVariables, TestLogger)

        testHelper.createUser("cestmir")
        testHelper.createUser("jonas")
        val post = testHelper.createPost(userId = "cestmir")
        testHelper.createWatchActivity(
            post = post,
            assetId = "asset-id",
            "jonas",
            subscriptionActive = true,
        )

        underTest.consume(
            SubscriberStatusChanged(
                userId = "jonas",
                creatorId = "cestmir",
                statusChange = SubscriberStatusChange.UNSUBSCRIBED,
                refunded = false,
                refused = false,
                cancelledReason = "",
                ended = false,
                doNotNotify = false,
            ),
        )

        with(testContext.selectFrom(WATCH_ACTIVITY).fetchSingle()) {
            assertThat(this[WATCH_ACTIVITY.SUBSCRIPTION_ACTIVE]).isFalse()
        }
    }
}
