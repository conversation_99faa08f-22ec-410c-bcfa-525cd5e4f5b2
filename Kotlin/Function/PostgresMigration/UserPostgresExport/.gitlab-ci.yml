Kotlin/Function/PostgresMigration/UserPostgresExport/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/PostgresMigration/UserPostgresExport/variables:
  variables:
    FUNCTION_NAME: "user-postgres-export"
    CLASS_NAME: "hero.functions.UserPostgresExport"
    ADDITIONAL_PARAMETERS: "--retry"

Kotlin/Function/PostgresMigration/UserPostgresExport/deploy-devel:
  needs:
    - Kotlin/Function/PostgresMigration/UserPostgresExport/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/PostgresMigration/UserPostgresExport/variables
  variables:
    TRIGGER: "$FIRESTORE_TRIGGER=devel-users/{userId}"

Kotlin/Function/PostgresMigration/UserPostgresExport/deploy-prod:
  needs:
    - Kotlin/Function/PostgresMigration/UserPostgresExport/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/PostgresMigration/UserPostgresExport/variables
  variables:
    TRIGGER: "$FIRESTORE_TRIGGER=prod-users/{userId}"
