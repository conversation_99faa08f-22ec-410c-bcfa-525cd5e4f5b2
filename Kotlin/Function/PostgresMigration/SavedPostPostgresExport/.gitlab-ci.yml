Kotlin/Function/PostgresMigration/SavedPostPostgresExport/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/IntegrationTesting/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/PostgresMigration/SavedPostPostgresExport/variables:
  variables:
    FUNCTION_NAME: "saved-post-postgres-export"
    CLASS_NAME: "hero.functions.SavedPostPostgresExport"
    ADDITIONAL_PARAMETERS: "--retry"

Kotlin/Function/PostgresMigration/SavedPostPostgresExport/deploy-devel:
  needs:
    - Kotlin/Function/PostgresMigration/SavedPostPostgresExport/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/PostgresMigration/SavedPostPostgresExport/variables
  variables:
    TRIGGER: "$FIRESTORE_TRIGGER=devel-saved-posts/{postId}"

Kotlin/Function/PostgresMigration/SavedPostPostgresExport/deploy-prod:
  needs:
    - Kotlin/Function/PostgresMigration/SavedPostPostgresExport/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/PostgresMigration/SavedPostPostgresExport/variables
  variables:
    TRIGGER: "$FIRESTORE_TRIGGER=prod-saved-posts/{postId}"
