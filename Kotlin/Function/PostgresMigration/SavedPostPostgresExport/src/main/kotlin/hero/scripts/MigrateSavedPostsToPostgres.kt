package hero.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.retryOn
import hero.core.data.Sort
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.SavedPost
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import hero.sql.jooq.Tables.SAVED_POST
import hero.sql.jooq.tables.records.SavedPostRecord
import java.util.concurrent.ExecutionException

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val firestore = firestore(SystemEnv.cloudProject, true)
    val savedPostsCollection = firestore.typedCollectionOf(SavedPost)
    var lastId: String? = null
    var processed = 0

    do {
        println("Processing saved posts from id $lastId")
        val savedPosts = retryOn(ExecutionException::class) {
            savedPostsCollection.where(SavedPost::id).isNotEqualTo("")
                .orderBy(SavedPost::id, Sort.Direction.DESC)
                .limit(200)
                .startAfterIfNotNull(lastId)
                .fetchAll()
        }

        if (savedPosts.isEmpty()) {
            break
        }

        val records = savedPosts.map {
            SavedPostRecord().apply {
                id = it.id
                userId = it.userId
                postId = it.postId
                creatorId = it.creatorId
                subscriptionActive = it.subscriptionActive
                savedAt = it.savedAt
                postPublishedAt = it.postPublishedAt
                deletedAt = null
            }
        }

        val ids = savedPosts.map { it.id }

        context
            .deleteFrom(SAVED_POST)
            .where(SAVED_POST.ID.`in`(ids))
            .execute()

        context
            .insertInto(SAVED_POST)
            .set(records)
            .execute()

        processed += savedPosts.size
        lastId = savedPosts.last().id
        println("Processed $processed saved posts")
    } while (true)
}
