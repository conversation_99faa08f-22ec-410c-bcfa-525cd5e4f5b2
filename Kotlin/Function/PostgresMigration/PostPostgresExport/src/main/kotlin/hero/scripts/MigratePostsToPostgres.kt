package hero.scripts

import hero.baseutils.SystemEnv
import hero.baseutils.retryOn
import hero.core.data.Sort
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Post
import hero.repository.post.PostRepository
import hero.sql.ConnectorConnectionPool
import hero.sql.jooq.JooqSQL
import java.util.concurrent.ExecutionException

fun main() {
    val context = JooqSQL.context(ConnectorConnectionPool.dataSource)
    val firestore = firestore(SystemEnv.cloudProject, true)
    val postsCollection = firestore.typedCollectionOf(Post)
    val postRepository = PostRepository(context)
    var lastId: String? = null
    var processed = 0

    do {
        println("Processing posts from id $lastId")
        val posts = retryOn(ExecutionException::class) {
            postsCollection.where(Post::id).isNotEqualTo("")
                .orderBy(Post::id, Sort.Direction.DESC)
                .limit(200)
                .startAfterIfNotNull(lastId)
                .fetchAll()
        }

        if (posts.isEmpty()) {
            break
        }

        try {

            postRepository.saveAll(posts)
        } catch (e: Exception) {
            println("ERROR: ${e.message}, ${e.stackTrace}")
        }

        processed += posts.size
        lastId = posts.last().id
        println("Processed $processed posts")
    } while (true)
}
