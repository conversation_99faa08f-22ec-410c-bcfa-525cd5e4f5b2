package hero.functions

import hero.model.SupportCounts
import hero.model.topics.SubscriberStatusChange
import hero.model.topics.SubscriberStatusChanged
import hero.sql.jooq.Tables
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestEnvironmentVariables
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SubscriptionSubscribeRequestsHandlerIT : IntegrationTest() {
    @Test
    fun `should remove pending request and decrease pending request count on creator`() {
        val underTest = SubscriptionSubscribeRequestsHandler(
            systemEnv = TestEnvironmentVariables,
            firestore = firestore,
            lazyContext = lazyTestContext,
            usersCollection = TestCollections.usersCollection,
            logger = TestLogger,
        )

        testHelper.createUser("cestmir", counts = SupportCounts(pendingRequests = 2))
        testHelper.createUser("pablo")
        testHelper.createSubscribeRequest("pablo", "cestmir")

        underTest.consume(
            SubscriberStatusChanged(
                userId = "pablo",
                creatorId = "cestmir",
                statusChange = SubscriberStatusChange.SUBSCRIBED,
                refused = false,
                cancelledReason = null,
                refunded = false,
                ended = false,
                doNotNotify = false,
            ),
        )

        // we have to assert using firestore, since we update firestore entity, not postgres
        assertThat(TestCollections.usersCollection["cestmir"].get().counts.pendingRequests).isEqualTo(1)

        val subscribeRequest = testContext.selectFrom(Tables.SUBSCRIBE_REQUEST).fetchSingle()
        assertThat(subscribeRequest.deletedAt).isNotNull()
    }
}
