package hero.functions

import com.stripe.model.Charge
import com.stripe.model.PaymentMethod
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.root
import hero.gcloud.where
import hero.model.Creator
import hero.model.Currency
import hero.model.User
import hero.model.topics.BigQueryWriteRequest
import hero.sql.jooq.tables.Charge.CHARGE
import hero.stripe.service.StripeClients
import org.jooq.DSLContext
import java.time.Instant

class StripeChargeStatisticsHandler(
    private val pubSub: PubSub,
    private val usersCollection: TypedCollectionReference<User>,
    private val stripeClients: StripeClients,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun handle(charge: Charge) {
        if (charge.status !in listOf("succeeded", "failed")) {
            return
        }
        if (charge.customer == null) {
            log.info("Charge ${charge.id} is missing customer id, skipping writing to BigQuery")
            return
        }

        val paymentMethod = stripeClients[charge.currency].paymentMethods().retrieve(charge.paymentMethod)

        pubSub.publish(
            BigQueryWriteRequest(
                id = charge.id,
                table = "stripe-charges",
                model = mapOf(
                    // https://dashboard.stripe.com/{test}/payments/{charge.id}
                    "chargeId" to charge.id,
                    "status" to charge.status,
                    "amount" to charge.amount,
                    "customerId" to charge.customer,
                    "paymentIntentId" to charge.paymentIntent,
                    "paymentMethodId" to charge.paymentMethod,
                    "paymentMethodType" to paymentMethod.metadata["cardCreateType"],
                    "cardBrand" to charge.paymentMethodDetails.card?.brand,
                    "cardCountry" to charge.paymentMethodDetails.card?.country,
                    "card3DS" to (charge.paymentMethodDetails.card?.threeDSecure?.result),
                    "cardCvcCheck" to charge.paymentMethodDetails.card?.checks?.cvcCheck,
                    "succeededCharges" to 1,
                    "status" to charge.status,
                    "failureCode" to charge.failureCode,
                    "failureMessage" to charge.failureMessage,
                    "timestamp" to Instant.ofEpochSecond(charge.created).toString(),
                    "currency" to charge.currency.uppercase(),
                ),
                updatable = false,
            ),
        )

        writeToSQL(charge, paymentMethod)
    }

    fun handle(
        chargeId: String?,
        paymentMethodId: String,
        customerId: String,
        succeededCharges: Int,
        declineCode: String?,
        declineMessage: String?,
        currency: Currency,
    ) {
        val paymentMethod = stripeClients[currency].paymentMethods().retrieve(paymentMethodId)
        pubSub.publish(
            BigQueryWriteRequest(
                id = chargeId,
                table = "stripe-charges",
                model = mapOf(
                    "chargeId" to chargeId,
                    "amount" to null,
                    "customerId" to customerId,
                    "paymentMethodId" to paymentMethodId,
                    "paymentMethodType" to paymentMethod.metadata["cardCreateType"],
                    "succeededCharges" to succeededCharges,
                    "paymentIntentId" to null,
                    "status" to "failed",
                    "failureCode" to declineCode,
                    "failureMessage" to declineMessage,
                    "timestamp" to Instant.now().toString(),
                    "currency" to null,
                ),
                updatable = false,
            ),
        )
    }

    private fun writeToSQL(
        charge: Charge,
        paymentMethod: PaymentMethod,
    ) {
        if (charge.customer == null) {
            log.warn("Charge ${charge.id} is missing customer id, not storing in SQL")
            return
        }

        if (charge.transferData?.destination == null) {
            log.warn("Missing destination account id for charge ${charge.id}, not storing in SQL")
            return
        }

        val destination = charge.transferData.destination
        val creator = usersCollection
            .where(root(User::creator).path(Creator::stripeAccountId)).isEqualTo(destination)
            .fetchSingle()
            ?: usersCollection
                .where(root(User::creator).path(Creator::stripeAccountLegacyIds)).contains(destination)
                .fetchSingle()

        if (creator == null) {
            log.warn("Failed to find creator for charge ${charge.id}")
        }

        context
            .insertInto(CHARGE)
            .set(CHARGE.STRIPE_ID, charge.id)
            .set(CHARGE.CUSTOMER_ID, charge.customer)
            .set(CHARGE.TRANSFER_AMOUNT, charge.transferData?.amount?.toInt())
            .set(CHARGE.TRANSFER_DESTINATION, destination)
            .set(CHARGE.CREATOR_ID, creator?.id)
            .set(CHARGE.AMOUNT, charge.amount)
            .set(CHARGE.AMOUNT_CAPTURED, charge.amountCaptured)
            .set(CHARGE.AMOUNT_REFUNDED, charge.amountRefunded)
            .set(CHARGE.PAYMENT_INTENT_ID, charge.paymentIntent)
            .set(CHARGE.STATUS, charge.status)
            .set(CHARGE.STRIPE_CREATED_AT, Instant.ofEpochSecond(charge.created))
            .set(CHARGE.PAYMENT_METHOD_ID, charge.paymentMethod)
            .set(CHARGE.CURRENCY, charge.currency.uppercase())
            .set(CHARGE.REFUNDED, charge.refunded)
            .set(CHARGE.DISPUTED, charge.disputed)
            .set(CHARGE.DESCRIPTION, charge.description)
            .set(CHARGE.PAYMENT_METHOD_TYPE, paymentMethod.metadata["cardCreateType"])
            .set(CHARGE.CARD_BRAND, charge.paymentMethodDetails.card?.brand)
            .set(CHARGE.CARD_COUNTRY, charge.paymentMethodDetails.card?.country)
            .set(CHARGE.CARD3DS, charge.paymentMethodDetails.card?.threeDSecure?.result)
            .set(CHARGE.CARD_CVC_CHECK, charge.paymentMethodDetails.card?.checks?.cvcCheck)
            .set(CHARGE.FAILURE_CODE, charge.failureCode)
            .set(CHARGE.FAILURE_MESSAGE, charge.failureMessage)
            .onConflict(CHARGE.STRIPE_ID)
            .doUpdate()
            .set(CHARGE.STATUS, charge.status)
            .set(CHARGE.TRANSFER_AMOUNT, charge.transferData?.amount?.toInt())
            .set(CHARGE.TRANSFER_DESTINATION, destination)
            .set(CHARGE.CREATOR_ID, creator?.id)
            .set(CHARGE.AMOUNT_CAPTURED, charge.amountCaptured)
            .set(CHARGE.AMOUNT_REFUNDED, charge.amountRefunded)
            .set(CHARGE.REFUNDED, charge.refunded)
            .set(CHARGE.DISPUTED, charge.disputed)
            .set(CHARGE.FAILURE_CODE, charge.failureCode)
            .set(CHARGE.FAILURE_MESSAGE, charge.failureMessage)
            .execute()
    }
}
