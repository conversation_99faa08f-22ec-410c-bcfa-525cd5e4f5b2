package hero.functions

import com.fasterxml.jackson.annotation.JsonProperty
import com.github.doyaaaaaken.kotlincsv.client.CsvReader
import com.github.kittinunf.fuel.httpGet
import com.stripe.param.ChargeUpdateParams
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.exceptions.http.ServerException
import hero.jackson.to
import hero.model.Currency
import hero.stripe.model.StripeFeeCsvReportGenerated
import hero.stripe.service.StripeClients
import hero.stripe.service.stripeRetry
import java.math.BigDecimal
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Suppress("unused")
class StripeInterchangeFeesWriter(
    private val stripeClients: StripeClients = StripeClients(SystemEnv.stripeKeyEu, SystemEnv.stripeKeyUs),
) : PubSubSubscriber<StripeFeeCsvReportGenerated>() {
    override fun consume(payload: StripeFeeCsvReportGenerated) {
        val executor = Executors.newFixedThreadPool(16)

        log.info("Writing fees based on report: ${payload.url}")
        var writes = 0
        var lastChargeId = ""
        try {
            fetchCsv(payload.currency, payload.url)
                // there may be some general fees unrelated to charges
                .filter { it.chargeId.isNotBlank() }
                .groupBy { it.chargeId }
                .forEach {
                    executor.submit {
                        write(it.key, payload.currency, it.value)
                        writes++
                        lastChargeId = it.key
                    }
                }
        } catch (e: IllegalArgumentException) {
            if ("Negative initial size" in e.message!!) {
                log.fatal("Report is too big and cannot be parsed: ${payload.url}", cause = e)
            } else {
                throw e
            }
        } catch (e: Exception) {
            throw ServerException("Cannot process CSV payload: ${payload.url}: ${e.message}", cause = e)
        }
        executor.shutdown()
        executor.awaitTermination(Long.MAX_VALUE, TimeUnit.HOURS)
        log.info("Updated $writes charges (last one was $lastChargeId): ${payload.url}")
    }

    private fun fetchCsv(
        currency: Currency,
        url: String,
    ): List<FeeDetails> {
        val key = when (currency) {
            Currency.EUR -> stripeClients.keyEu
            Currency.USD -> stripeClients.keyUs
            else -> error("Invalid currency: $currency")
        }

        return url
            .httpGet()
            .header("Authorization", "Bearer $key")
            .response()
            .second
            .data
            .let { String(it) }
            .let { CsvReader().readAllWithHeader(it) }
            .map { it.to<FeeDetails>() }
    }

    private fun write(
        chargeId: String,
        currency: Currency,
        fees: List<FeeDetails>,
    ) {
        (fees + fees.map { it.copy(feeCategory = "total") })
            .groupBy { it.feeCategory }
            .toList()
            .associate {
                "fee_" + it.first to it.second.sumOf { it.totalAmount }.toString()
            }
            .let {
                writeMeta(chargeId, currency, it)
            }
    }

    internal fun writeMeta(
        chargeId: String,
        currency: Currency,
        updates: Map<String, String>,
    ) {
        try {
            stripeRetry {
                stripeClients[currency]
                    .charges()
                    .update(
                        chargeId,
                        ChargeUpdateParams.builder()
                            .setMetadata(updates)
                            .build(),
                    )
            }
        } catch (e: Exception) {
            log.fatal("Cannot update $chargeId with $updates: ${e.message}", cause = e)
        }
    }
}

data class FeeDetails(
    @JsonProperty("charge_id")
    val chargeId: String,
    @JsonProperty("billing_currency")
    val billingCurrency: String,
    @JsonProperty("total_amount")
    val totalAmount: BigDecimal,
    @JsonProperty("card_country")
    val cardCountry: String,
    @JsonProperty("fee_category")
    val feeCategory: String,
    @JsonProperty("fee_name")
    val feeName: String,
    @JsonProperty("plan_name")
    val planName: String,
    @JsonProperty("event_type")
    val eventType: String,
)
