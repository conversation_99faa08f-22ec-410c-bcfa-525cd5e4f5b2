# Reversing unreversed refunds

When user is charged this is the flow of money:

1. Money arrives into Herohero account
2. Herohero keeps 10%
3. Herohero sends 90% to the related connected account

When refunding, we need to perform all three steps above in a negative way. If the admin
only refunds the charge [case A], it will basically only performs inverse operation to (1), but money
stays in the connected account.

It may also happen, that reversing a transaction in the connected account [case B] will only perform
point (3) but the money are not refunded to the customer.

This consumer reverses fixes [case A] and performs inverse operation to (3) from connected accounts
in case these were not performed. It does not fix [case B] as we don't expect it to happen.
