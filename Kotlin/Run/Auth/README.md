# Auth service

To setup redirect urls, see these links:

- [Google login via API OAuth client](https://console.cloud.google.com/apis/credentials/oauthclient/***********-sr2tsr7f2jojp0rgfo6gjg5vhseo4e4q.apps.googleusercontent.com?project=heroheroco)
- [Facebook login via Facebook App](https://developers.facebook.com/apps/***************/fb-login/settings/?business_id=***************)
- [Discord app](https://discord.com/developers/applications/970573270548111380/information)

# Apple private relay emails (Hide-my-email feature)

- Domains must be correctly set at: https://developer.apple.com/account/resources/services/configure
  - mg.herohero.co (mailgun)
  - herohero.co (our google-workspace emails)
- For testing, you can remove your previous Apple logins at: https://account.apple.com/account/manage/section/security
- Manage your Hide-my-emails at: https://www.icloud.com/icloudplus/

# Firebase authentication
### Brute forcing email password
Firebase has it's own protection against password brute forcing, attempting to brute force a password results in
```text
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt zbq
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt aem
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt aex
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt dom
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt aij
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt am4
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt 6u
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt rke
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt nn
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt n8
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt k3
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt g5e
Firebase: Exceeded quota for signing in with passwords. (auth/quota-exceeded).for attempt bld
Firebase: Error (auth/network-request-failed).for attempt ba4h
Firebase: Error (auth/network-request-failed).for attempt ba4i
Firebase: Error (auth/network-request-failed).for attempt ba4j
Firebase: Error (auth/network-request-failed).for attempt ba4k
Firebase: Error (auth/network-request-failed).for attempt ba4l
Firebase: Error (auth/network-request-failed).for attempt ba4m
Firebase: Error (auth/network-request-failed).for attempt ba4n
Firebase: Error (auth/network-request-failed).for attempt ba4o
Firebase: Error (auth/network-request-failed).for attempt ba4p
Firebase: Error (auth/network-request-failed).for attempt ba4q
Firebase: Error (auth/network-request-failed).for attempt ba4r
Firebase: Error (auth/network-request-failed).for attempt ba4s
```

Adding a delay for 10 ms, for example, does not trigger this protection though. But with this delay it's almost impossible
to crack a password.
