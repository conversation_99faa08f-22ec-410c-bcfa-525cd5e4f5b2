package hero.api.statistics.service

import hero.api.gjirafaAsset
import hero.baseutils.minusDays
import hero.exceptions.http.ForbiddenException
import hero.gjirafa.GjirafaStatsService
import hero.model.PostAsset
import hero.model.topics.PostState
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.LocalDate

class PostStatisticsQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetCreatorsMostViewedPosts {
        @Test
        fun `should return most viewed published posts`() {
            val gjirafaService = mockk<GjirafaStatsService>()
            val underTest = PostStatisticsQueryService(
                TestCollections.postsCollection,
                gjirafaService,
                lazyTestContext,
            )
            val post1 = testHelper.createPost(userId = "ouksubunfejae", id = "post-1")
            val post2 = testHelper.createPost(userId = "ouksubunfejae", id = "post-2")
            // post-3 has the least views
            testHelper.createPost(userId = "ouksubunfejae", id = "post-3")
            val post4 = testHelper.createPost(userId = "ouksubunfejae", id = "post-4")
            val post5 = testHelper.createPost(userId = "ouksubunfejae", id = "post-5")
            val post6 = testHelper.createPost(userId = "ouksubunfejae", id = "post-6")

            // deleted post
            testHelper.createPost(userId = "ouksubunfejae", id = "post-7", state = PostState.DELETED)

            testContext.execute(
                """
insert into public.daily_post_view_statistics (date, post_id, creator_id, views)
values  ('2024-06-18', 'post-1', 'ouksubunfejae', 10),
        ('2024-06-19', 'post-1', 'ouksubunfejae', 10),
        ('2024-06-20', 'post-1', 'ouksubunfejae', 10),
        
        ('2024-06-18', 'post-2', 'ouksubunfejae', 20),
        ('2024-06-19', 'post-2', 'ouksubunfejae', 20),
        ('2024-06-20', 'post-2', 'ouksubunfejae', 20),
        
        -- least viewed post should be ignored because we fetch only 5 items
        ('2024-06-18', 'post-3', 'ouksubunfejae', 1),
        ('2024-06-19', 'post-3', 'ouksubunfejae', 1),
        ('2024-06-20', 'post-3', 'ouksubunfejae', 1),
        
        ('2024-06-18', 'post-4', 'ouksubunfejae', 40),
        ('2024-06-19', 'post-4', 'ouksubunfejae', 40),
        ('2024-06-20', 'post-4', 'ouksubunfejae', 40),
        
        ('2024-06-18', 'post-5', 'ouksubunfejae', 60),
        ('2024-06-19', 'post-5', 'ouksubunfejae', 60),
        ('2024-06-20', 'post-5', 'ouksubunfejae', 60),
        
        ('2024-06-18', 'post-6', 'ouksubunfejae', 50),
        ('2024-06-19', 'post-6', 'ouksubunfejae', 50),
        ('2024-06-20', 'post-6', 'ouksubunfejae', 50),
        
        ('2024-06-20', 'post-7', 'ouksubunfejae', 50)
                """,
            )

            val result = underTest.execute(
                GetCreatorsMostViewedPosts(
                    "ouksubunfejae",
                    // Tuesday, 18 June 2024 00:00:00
                    Instant.ofEpochSecond(1718668800),
                    // Tuesday, 20 June 2024 00:00:00
                    Instant.ofEpochSecond(1718841600),
                    5,
                ),
            )

            assertThat(result).containsExactly(
                PostWithViewStats(post5, PostViewStats("post-5", 180)),
                PostWithViewStats(post6, PostViewStats("post-6", 150)),
                PostWithViewStats(post4, PostViewStats("post-4", 120)),
                PostWithViewStats(post2, PostViewStats("post-2", 60)),
                PostWithViewStats(post1, PostViewStats("post-1", 30)),
            )
        }

        @Test
        fun `if creator has no post views, then empty list should be returned`() {
            val gjirafaService = mockk<GjirafaStatsService>()
            val underTest = PostStatisticsQueryService(
                TestCollections.postsCollection,
                gjirafaService,
                lazyTestContext,
            )
            testHelper.createPost(userId = "ouksubunfejae", id = "post-1")
            testHelper.createPost(userId = "ouksubunfejae", id = "post-2")

            val result = underTest.execute(
                GetCreatorsMostViewedPosts("ouksubunfejae", Instant.now().minusDays(10), Instant.now(), 5),
            )

            assertThat(result).isEmpty()
        }
    }

    @Nested
    inner class GetPostViewStats {
        @Test
        fun `should correctly calculate stats for a single post and fetch relevant stats from gjirafa`() {
            val gjirafaService = mockk<GjirafaStatsService>().apply {
                every { assetPlays(any(), any(), any()) } returns 2.0
                every { assetCompletePlays(any(), any(), any()) } returns 3.0
                every { assetAverageViewDuration(any()) } returns 4.0
            }
            val underTest = PostStatisticsQueryService(
                TestCollections.postsCollection,
                gjirafaService,
                lazyTestContext,
            )
            val post = testHelper.createPost(
                userId = "ouksubunfejae",
                id = "ouksubunfejaezvlpfyspaobpsulg",
                assets = listOf(PostAsset(gjirafa = gjirafaAsset("vjsnrkvt"))),
            )
            testContext.execute(
                """
insert into public.daily_post_view_statistics (date, post_id, creator_id, views)
values  ('2024-06-18', 'ouksubunfejaezvlpfyspaobpsulg', 'ouksubunfejae', 13),
        ('2024-06-19', 'ouksubunfejaezvlpfyspaobpsulg', 'ouksubunfejae', 5),
        ('2024-06-20', 'ouksubunfejaezvlpfyspaobpsulg', 'ouksubunfejae', 8)
                """,
            )
            testHelper.createSavedPost("ouksubunfejae", post)

            val result = underTest.execute(
                GetPostViewStats(userId = "ouksubunfejae", postId = "ouksubunfejaezvlpfyspaobpsulg"),
            )

            assertThat(result.post).isEqualTo(post)
            assertThat(result.stats).isEqualTo(
                PostCompleteStats(
                    "ouksubunfejaezvlpfyspaobpsulg",
                    26,
                    1,
                    listOf(AssetStats(assetId = "vjsnrkvt", plays = 2, completes = 3, 4.0)),
                ),
            )
            verify(exactly = 1) {
                gjirafaService.assetPlays("vjsnrkvt", any(), LocalDate.now())
                gjirafaService.assetCompletePlays("vjsnrkvt", any(), LocalDate.now())
                gjirafaService.assetAverageViewDuration("vjsnrkvt")
            }
        }

        @Test
        fun `only creator can see stats of his posts`() {
            val gjirafaService = mockk<GjirafaStatsService>()
            val underTest = PostStatisticsQueryService(
                TestCollections.postsCollection,
                gjirafaService,
                lazyTestContext,
            )
            testHelper.createPost(userId = "ouksubunfejae", id = "ouksubunfejaezvlpfyspaobpsulg")

            assertThatExceptionOfType(ForbiddenException::class.java).isThrownBy {
                underTest.execute(
                    GetPostViewStats(userId = "someone-else", postId = "ouksubunfejaezvlpfyspaobpsulg"),
                )
            }
        }

        @Test
        fun `should return zero views if post has no stats yet`() {
            val gjirafaService = mockk<GjirafaStatsService>()
            val underTest = PostStatisticsQueryService(
                TestCollections.postsCollection,
                gjirafaService,
                lazyTestContext,
            )
            val post = testHelper.createPost(userId = "ouksubunfejae", id = "ouksubunfejaezvlpfyspaobpsulg")

            val result = underTest.execute(
                GetPostViewStats(userId = "ouksubunfejae", postId = "ouksubunfejaezvlpfyspaobpsulg"),
            )

            assertThat(result.post).isEqualTo(post)
            assertThat(result.stats).isEqualTo(PostCompleteStats("ouksubunfejaezvlpfyspaobpsulg", 0, 0, listOf()))
        }
    }
}
