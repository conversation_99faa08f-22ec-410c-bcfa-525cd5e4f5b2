package hero.api.user.service

import hero.core.data.PageRequest
import hero.model.SupportCounts
import hero.test.IntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SearchUsersQueryServiceIT : IntegrationTest() {
    @Test
    fun `my subscribed creators should be first and then order by subscribers count`() {
        val underTest = SearchUsersQueryService(lazyTestContext)

        val creator1 = testHelper.createUser("cestmir1", name = "<PERSON>estm<PERSON><PERSON>rak<PERSON>", counts = SupportCounts(10))
        val creator2 = testHelper.createUser("cestmir2", name = "cestmir fialovy", counts = SupportCounts(50))
        val creator3 = testHelper.createUser("cestmir3", name = "4estmir mourovaty", counts = SupportCounts(30))
        val creator4 = testHelper.createUser("cestmir4", name = "čestmi", counts = SupportCounts(40))

        testHelper.createUser("pepa")
        testHelper.createSubscription(userId = "pepa", creatorId = "cestmir1")

        val result = underTest.execute(SearchUsers("čestmír", "pepa", PageRequest()))

        assertThat(result.content).containsExactly(creator1, creator2, creator4, creator3)
    }
}
