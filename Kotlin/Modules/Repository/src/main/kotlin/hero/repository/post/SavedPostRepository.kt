package hero.repository.post

import hero.model.SavedPost
import hero.sql.jooq.Tables.SAVED_POST
import hero.sql.jooq.tables.records.SavedPostRecord
import org.jooq.DSLContext

class SavedPostRepository(
    lazyContext: <PERSON>zy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    constructor(context: DSLContext) : this(lazy { context })

    fun save(savedPost: SavedPost): SavedPost {
        val savedPostRecord = SavedPostRecord().apply {
            id = savedPost.id
            userId = savedPost.userId
            postId = savedPost.postId
            creatorId = savedPost.creatorId
            subscriptionActive = savedPost.subscriptionActive
            savedAt = savedPost.savedAt
            postPublishedAt = savedPost.postPublishedAt
            deletedAt = null
        }

        context
            .insertInto(SAVED_POST)
            .set(savedPostRecord)
            .onDuplicateKeyUpdate()
            .set(savedPostRecord)
            .execute()

        return savedPost
    }
}
