CREATE TABLE post
(
    id                TEXT PRIMARY KEY NOT NULL,
    user_id           TEXT             NOT NULL,
    parent_id         TEXT             NULL,
    sibling_id        TEXT             NULL,
    parent_user_id    TEXT             NULL,
    parent_post_id    TEXT             NULL,
    message_thread_id TEXT             NULL,

    -- timestamps
    created_at        TIMESTAMPTZ      NOT NULL,
    updated_at        TIMESTAMPTZ      NOT NULL,
    published_at      TIMESTAMPTZ      NOT NULL,
    pinned_at         TIMESTAMPTZ      NULL,
    notified_at       TIMESTAMPTZ      NULL,
    deleted_at        TIMESTAMPTZ      NULL,

    state             TEXT             NOT NULL,
    type              TEXT             NOT NULL,
    text              TEXT             NOT NULL,
    text_html         TEXT             NULL,
    text_delta        TEXT             NULL,
    comments_count    INT              NOT NULL,
    replies_count     INT              NOT NULL,

    price             INT              NULL,

    categories        TEXT[]           NOT NULL,
    chapters          JSONB[]          NOT NULL,

    -- flags
    exclude_from_rss  BOOLEAN          NOT NULL,
    is_age_restricted BOOLEAN          NOT NULL,
    is_sponsored      BOOLEAN          NOT NULL,

    CONSTRAINT "19be0d976e14478d958a96a6c3246b6a_fk" FOREIGN KEY (user_id) REFERENCES "user" (id)
);


-- add foreign key constraints on parent_id, etc. after all data are migrated

CREATE TABLE post_asset
(
    id                          SERIAL PRIMARY KEY NOT NULL,
    post_id                     TEXT               NOT NULL,
    thumbnail                   TEXT               NULL,
    asset_type                  TEXT               NOT NULL,
    metadata                    JSONB              NOT NULL,
    content_moderation_metadata JSONB              NULL,

    CONSTRAINT "f9b0bc2920024266b7d19a34b6519954_fk" FOREIGN KEY (post_id) REFERENCES post (id)
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE post TO "<EMAIL>";
            GRANT ALL ON TABLE post_asset TO "<EMAIL>";
        END IF;
    END
$$;
