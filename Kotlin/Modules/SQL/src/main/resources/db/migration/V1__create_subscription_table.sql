CREATE TABLE subscription
(
    stripe_id       TEXT PRIMARY KEY,
    customer_id     TEXT        NOT NULL,
    creator_id      TEXT        NOT NULL,
    created_at      TIMESTAMPTZ NOT NULL,
    ends_at         TIMESTAMPTZ NOT NULL,
    status          TEXT        NOT NULL,
    currency        TEXT        NOT NULL,
    user_id         TEXT        NOT NULL,
    creator_country TEXT,
    tier_id         TEXT        NOT NULL,
    price_cents     BIGINT
);

DO
$$
    BEGIN
        IF EXISTS (SELECT 1 FROM pg_user WHERE usename = '<EMAIL>') THEN
            GRANT ALL ON TABLE subscription TO "<EMAIL>";
        END IF;
    END
$$;
