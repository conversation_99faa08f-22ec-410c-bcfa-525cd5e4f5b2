package hero.gjirafa

import com.github.kittinunf.fuel.httpDelete
import com.github.kittinunf.fuel.httpGet
import com.github.kittinunf.fuel.httpPost
import com.github.kittinunf.fuel.httpPut
import hero.baseutils.FuelException
import hero.baseutils.fetch
import hero.baseutils.log
import hero.baseutils.retryOn
import hero.baseutils.systemEnv
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.exceptions.http.HttpStatusException
import hero.exceptions.http.NotFoundException
import hero.exceptions.http.ServerException
import hero.gjirafa.dto.DeleteAudiosRequest
import hero.gjirafa.dto.GjirafaMultipartPart
import hero.gjirafa.dto.MediaItemRequest
import hero.jackson.toJson
import hero.model.GjirafaAsset
import hero.model.GjirafaAssetType
import hero.model.GjirafaAssetType.AUDIO
import hero.model.GjirafaAssetType.VIDEO
import hero.model.GjirafaEncodeRequest
import hero.model.GjirafaEncodeResponseResult
import hero.model.GjirafaMediaDetail
import hero.model.GjirafaMediaDetailV2
import hero.model.GjirafaResponse
import hero.model.GjirafaResponsePage
import hero.model.GjirafaStatus.COMPLETE
import hero.model.GjirafaStatus.PARTIALLY_COMPLETED
import hero.model.GjirafaUpload
import hero.model.GjirafaUploadInfo
import hero.model.GjirafaUploadResponseResult
import java.io.IOException
import java.time.temporal.ChronoUnit

class GjirafaUploadsService(
    private val projectId: String,
    private val apiKey: String,
    private val imageKey: String,
) {
    // https://client.vp.gjirafa.tech/project-id/api-keys

    // 1. upload
    // https://vp.gjirafa.tech/documentation/api/upload-api/post/
    fun uploadsUrl(
        userId: String,
        mimeType: String,
    ): GjirafaUploadResponseResult =
        try {
            "${VIDEO_BASE_URL}/$projectId/uploads/url".httpPost()
                .authorize(apiKey)
                .body(
                    mapOf(
                        "fileName" to "$userId-${System.currentTimeMillis()}.mp4",
                        "mimeType" to mimeType,
                        "contentType" to mimeType,
                    ).toJson(),
                )
                .fetch<GjirafaResponse<GjirafaUploadResponseResult>>()
                .result
        } catch (e: FuelException) {
            throw HttpStatusException(
                e.status,
                "Couldn't initiate $mimeType upload for $userId: ${e.message}",
                mapOf("userId" to userId),
                null,
            )
        }

    fun delete(assetId: String): Boolean =
        try {
            when {
                assetId.startsWith("v") ->
                    "${VIDEO_BASE_URL}/$projectId/videos"
                        .httpDelete(listOf("id" to assetId))

                assetId.startsWith("b") ->
                    "${AUDIO_BASE_URL}/$projectId/audios"
                        .httpDelete()
                        .body(DeleteAudiosRequest(listOf(assetId)).toJson())

                else -> throw ConflictException("Gjirafa asset must start with either 'v' or 'b'.")
            }
                .authorize(apiKey)
                .fetch<GjirafaResponse<Any>>()
                .success
        } catch (e: FuelException) {
            log.error(
                "Couldn't delete $assetId: ${e.message}",
                mapOf("assetId" to assetId),
                null,
            )
            false
        }

    fun uploadsMultipart(
        userId: String,
        partsNumber: Int,
        contentLength: Long,
        mimeType: String,
    ): GjirafaUploadInfo =
        try {
            "${VIDEO_BASE_URL}/$projectId/uploads/multipart".httpPost()
                .authorize(apiKey)
                .body(
                    mapOf(
                        "fileName" to "$userId-${System.currentTimeMillis()}.mp4",
                        "partsNumber" to partsNumber,
                        "contentLength" to contentLength,
                        "mimeType" to mimeType,
                        "contentType" to mimeType,
                    ).toJson(),
                )
                .fetch<GjirafaResponse<GjirafaUploadInfo>>()
                .result
        } catch (e: FuelException) {
            throw HttpStatusException(
                e.status,
                "Couldn't initiate media encoding by $userId: ${e.message}",
                mapOf("userId" to userId),
                null,
            )
        }

    fun encodeUrl(
        userId: String,
        assetUrl: String,
        assetType: GjirafaAssetType,
        hasDrm: Boolean?,
        encodingTemplateId: String?,
    ): GjirafaEncodeResponseResult {
        val result = try {
            when (assetType) {
                VIDEO -> "$VIDEO_BASE_URL/$projectId/encodes"
                AUDIO -> "$AUDIO_BASE_URL/$projectId/audios"
                else -> error("Cannot run encoding for $assetType")
            }
                .httpPost()
                .authorize(apiKey)
                .body(
                    mapOf(
                        "encodingTemplateId" to encodingTemplateId,
                        "hasDrm" to hasDrm,
                        "downloadUrl" to assetUrl,
                        "enableDuplicateCheck" to true,
                        "author" to userId,
                        "title" to "Media authored by $userId",
                    ).toJson(),
                )
                .fetch<GjirafaResponse<GjirafaEncodeResponseResult>>()
                .result
        } catch (e: FuelException) {
            throw HttpStatusException(
                e.status,
                "Couldn't initiate media encoding by $userId for $assetUrl/$assetType: ${e.message}",
                mapOf("userId" to userId),
                null,
            )
        }

        return result
    }

    /** See: https://ripe-gull-a38.notion.site/Multipart-Upload-dcb133d5ec46489f889e3b43a611e607 */
    fun completeMultipartUpload(
        userId: String,
        requestBody: GjirafaEncodeRequest,
    ): Boolean {
        if (requestBody.parts.isEmpty()) {
            throw BadRequestException(
                "Multipart request cannot have `parts` empty.",
                mapOf("userId" to userId),
            )
        }
        val partNumbers = requestBody.parts.map { it.partNumber }
        if (partNumbers != partNumbers.sorted()) {
            throw BadRequestException(
                "Parts were not sorted correctly.",
                mapOf("userId" to userId),
            )
        }
        val invalidIndex = requestBody.parts.indexOfFirst { it.eTag.isNullOrBlank() }
        if (invalidIndex != -1) {
            throw BadRequestException(
                "Multipart request is missing `parts[$invalidIndex]` or its e-tag.",
                mapOf("userId" to userId),
            )
        }
        return retryOn(IOException::class) {
            try {
                "${VIDEO_BASE_URL}/$projectId/uploads/multipart-complete"
                    .httpPut()
                    .authorize(apiKey)
                    .body(requestBody.toJson())
                    .fetch<GjirafaResponse<Boolean>>()
                    .result
            } catch (e: FuelException) {
                throw HttpStatusException(
                    status = e.status,
                    message = "Couldn't complete multipart upload for ${requestBody.parts.size} parts " +
                        "with key ${requestBody.requestKey}: ${e::class.simpleName}/${e.message}",
                    body = null,
                    cause = null,
                    labels = mapOf("userId" to userId),
                )
            }
        }
    }

    /**
     * https://vp.gjirafa.tech/documentation/api/manage-api/video/getVideos
     * https://vp.gjirafa.tech/documentation/api/manage-api/audio/API%20V1/getAudiosWithPagination
     * https://vp-api.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Admin%20API%20V1#/Video/get_api_projects__projectId__videos
     * https://audio.vp.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Audio%20API%20V1#/Audio/get_api_v1__projectId__audios
     */
    fun getAssetsByAuthor(author: String): List<GjirafaMediaDetailV2> {
        val videos = "$VIDEO_BASE_URL/$projectId/videos?author=$author"
            .httpGet()
            .authorize(apiKey)
            .fetch<GjirafaResponse<GjirafaResponsePage<GjirafaMediaDetailV2>>>()
            .result
            .items
        val audios = "$AUDIO_BASE_URL/$projectId/audios?author=$author"
            .httpGet()
            .authorize(apiKey)
            .fetch<GjirafaResponse<GjirafaResponsePage<GjirafaMediaDetailV2>>>()
            .result
            .items

        return videos.plus(audios).sortedByDescending { it.title }
    }

    /**
     * @param userId pass user id if validation should be done against the passed value
     * https://vp.gjirafa.tech/documentation/api/manage-api/video/getVideoAssets
     * https://vp.gjirafa.tech/documentation/api/manage-api/audio/API%20V1/getAudioAssets
     * https://vp-api.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Admin%20API%20V1#/Video/get_api_projects__projectId__videos__videoId__assets
     * https://audio.vp.gjirafa.tech/swagger/index.html?urls.primaryName=VP%20Audio%20API%20V1#/Audio/get_api_v1__projectId__audios__audioId__assets
     */
    fun getAsset(
        userId: String?,
        assetId: String,
        withDebug: Boolean = false,
    ): GjirafaAsset {
        userId?.run { assertAuthor(userId, assetId) }

        val host = when {
            assetId.startsWith("v") -> "$VIDEO_BASE_URL/$projectId/videos/assets/$assetId?anyStatus=true"
            assetId.startsWith("b") -> "$AUDIO_BASE_URL/$projectId/audios/$assetId/assets?anyStatus=true"
            else -> throw ConflictException("Gjirafa asset must start with either 'v' or 'b'.")
        }

        val mediaDetail = try {
            host
                .httpGet()
                .authorize(apiKey)
                .fetch<GjirafaResponse<GjirafaMediaDetail>>()
                .result
        } catch (e: FuelException) {
            if (e.status == 404) {
                throw NotFoundException("Asset $assetId was not found.")
            }
            error("Couldn't get media $assetId: ${e::class.simpleName}/${e.message}")
        }

        val streamResponse = mediaDetail.streamResponse
            ?: throw ServerException("Asset $assetId was missing streamResponse.", mapOf("assetId" to assetId))

        if (streamResponse.streams.any { it.qualityTypes.any { it.status == null } }) {
            throw ServerException("Asset $assetId returned broken status.", mapOf("assetId" to assetId))
        }

        val videoStream = streamResponse.streams
            .firstOrNull { it.assetType == VIDEO }

        val videoHls = videoStream
            ?.qualityTypes
            ?.filter {
                it.status == COMPLETE || it.status == PARTIALLY_COMPLETED
            }
            ?.sortedByDescending { it.qualityType }
            ?.firstOrNull()

        val audioStream = streamResponse.streams
            .firstOrNull { it.assetType == AUDIO && it.streamType == "hls" }

        val audioHls = audioStream
            ?.qualityTypes
            ?.filter {
                it.status == COMPLETE || it.status == PARTIALLY_COMPLETED
            }
            ?.sortedByDescending { it.qualityType }
            ?.firstOrNull()

        val audioMp3 = streamResponse.streams
            .firstOrNull { it.assetType == AUDIO && (it.streamType == "mp3" || it.streamType == "mp4") }
            ?.qualityTypes
            ?.sortedByDescending { it.qualityType }
            ?.firstOrNull()

        // TODO this should ideally be replaced soon by our key generation method
        val extractedKey = (videoStream?.streamUrl ?: audioHls?.streamUrl)
            ?.replace("https://cdn.vpplayer.tech/[a-z]+/(.*?)/.*".toRegex(), "$1")

        return GjirafaAsset(
            id = assetId,
            width = videoHls?.width,
            height = videoHls?.height,
            duration = videoHls?.duration ?: audioHls?.duration ?: audioMp3?.duration ?: 0.0,
            // for videos we use general HLS playlist given in stream root
            videoStreamUrl = videoStream?.streamUrl ?: mediaDetail.playbackUrl,
            // for audios we use the top quality mp3/mp4 given in stream.qualityTypes
            audioStreamUrl = audioHls?.streamUrl,
            audioStaticUrl = audioMp3?.streamUrl,
            chaptersVttUrl = mediaDetail.chaptersVtt,
            key = extractedKey,
            // gjirafa may not provide `progressTillReadiness` for audios yet, fallbacking to `progress`
            // https://herohero-workspace.slack.com/archives/C04UCDB3CJK/p1746654917679569
            progressTillReadiness = streamResponse.progressTillReadiness ?: streamResponse.progress ?: 0,
            progressTillCompleteness = streamResponse.progress ?: 0,
            hasAudio = assetId.startsWith("b") || audioMp3 != null,
            hasVideo = assetId.startsWith("v"),
            audioByteSize = audioMp3?.size,
            hidden = false,
            status = streamResponse.status,
            debugDetail = if (withDebug) mediaDetail else null,
            keyId = streamResponse.keyId,
            encodingRemainingTime = streamResponse.encodingProcessRemainingTime,
            encodingFinishTime = streamResponse.encodingProcessFinishTime,
            createdAt = streamResponse.insertDate?.truncatedTo(ChronoUnit.SECONDS),
            projectId = projectId,
            imageKey = imageKey,
        )
    }

    fun getUpload(uploadId: String): GjirafaResponse<GjirafaUpload> =
        "https://vp-api.gjirafa.tech/api/projects/$projectId/uploads/url/$uploadId"
            .httpGet()
            .authorize(apiKey)
            .fetch<GjirafaResponse<GjirafaUpload>>()

    fun getParts(requestKey: String): List<GjirafaMultipartPart> =
        try {
            "${VIDEO_BASE_URL}/$projectId/uploads/url/multipart/$requestKey/parts"
                .httpGet()
                .authorize(apiKey)
                .fetch<GjirafaResponse<List<GjirafaMultipartPart>>>()
                .result
        } catch (e: FuelException) {
            throw HttpStatusException(
                status = e.status,
                message = e.message,
                body = null,
                cause = null,
                labels = emptyMap(),
            )
        }

    fun abortMultipart(requestKey: String): Boolean =
        try {
            "${VIDEO_BASE_URL}/$projectId/uploads/multipart-abort"
                .httpPut()
                .authorize(apiKey)
                .body(mapOf("requestKey" to requestKey).toJson())
                .fetch<GjirafaResponse<Boolean>>()
                .result
        } catch (e: FuelException) {
            if (e.status == 400) {
                // Gjirafa returns 400 when the multipart upload was not properly performed,
                // we just need to propagate the `false` value to FE,
                // which would be contained in GjirafaResponse<Boolean>.
                false
            } else {
                throw e
            }
        }

    /** WARN: Storing media meta details should only be used for testing or manual changes. */
    fun storeMeta(
        userId: String,
        assetId: String,
        // postId is not available at the time of asset creation
        postId: String?,
    ) {
        try {
            if (assetId.startsWith("v")) {
                "$VIDEO_BASE_URL/$projectId/videos"
            } else {
                "$AUDIO_BASE_URL/$projectId/audios"
            }
                .httpPut()
                .authorize(apiKey)
                .body(
                    MediaItemRequest(
                        title = "Media authored by $userId${if (postId != null) " in post $postId" else ""}.",
                        publicId = assetId,
                        author = userId,
                        description = postId,
                        chapterState = true,
                        chapters = emptyList(),
                    ).toJson(),
                )
                .fetch<Any>()
        } catch (e: FuelException) {
            throw HttpStatusException(
                status = e.status,
                message = e.message,
                body = null,
                cause = null,
                labels = mapOf("userId" to userId, "assetId" to assetId),
            )
        }
    }

    internal fun assertAuthor(
        userId: String,
        assetId: String,
    ) {
        if (!isAuthor(userId, assetId)) {
            throw ForbiddenException("User $userId is not an owner of $assetId.", mapOf("assetId" to assetId))
        }
    }

    fun isAuthor(
        userId: String,
        assetId: String,
    ): Boolean {
        val media = try {
            if (assetId.startsWith("v")) {
                "$VIDEO_BASE_URL/$projectId/videos/$assetId"
            } else {
                "$AUDIO_BASE_URL/$projectId/audios/$assetId"
            }
                .httpGet()
                .authorize(apiKey)
                .fetch<GjirafaResponse<GjirafaMediaDetailV2>>()
                .result
        } catch (e: FuelException) {
            throw HttpStatusException(
                status = e.status,
                message = e.message,
                body = null,
                cause = null,
                labels = mapOf("userId" to userId, "assetId" to assetId),
            )
        }

        return media.author == userId
    }

    fun setPermissions(
        permisssion: Int,
        assetId: String,
    ): GjirafaResponse<Any> {
        val root = if (assetId.startsWith("v"))
            "${VIDEO_BASE_URL}/$projectId/videos/$assetId/original-files"
        else
            "${AUDIO_BASE_URL}/$projectId/audios/$assetId/original-files"

        return root
            .httpPost()
            .authorize(apiKey)
            .body("{\"accessControl\": $permisssion}")
            .fetch<GjirafaResponse<Any>>()
    }

    fun getAssets(): List<GjirafaMediaDetailV2> {
        val audios = "$AUDIO_BASE_URL/$projectId/audios?take=1000"
            .httpGet()
            .authorize(apiKey)
            .fetch<GjirafaResponse<GjirafaResponsePage<GjirafaMediaDetailV2>>>()
            .result
            .items

        return audios
    }
}

fun main() {
    val srvc = GjirafaUploadsService(
        projectId = "agmipoda",
        apiKey = systemEnv("GJIRAFA_API_KEY"),
        imageKey = "123",
    )

    srvc
        .getAssets()
        .map { it.publicId }
        .forEach {
            println(it)
            srvc.delete(it)
        }
}
