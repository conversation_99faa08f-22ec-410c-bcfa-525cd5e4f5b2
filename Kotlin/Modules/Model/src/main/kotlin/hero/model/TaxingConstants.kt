package hero.model

/**
 * We are currently paying VAT in Czechia. We store this value to compare
 * if the creator is eligible for tax paying with us or they have to pay VAT
 * in their country of residence via 'reverse charge'.
 * Specification: https://linear.app/herohero/issue/HH-638/taxing-spec
 */
const val CZ_VAT_COUNTRY: String = "CZ"

/** Countries belonging to EU. */
val euCountries = arrayOf(
    "AT", // Austria
    "BE", // Belgium
    "BG", // Bulgaria
    "CY", // Cyprus
    "CZ", // Czechia
    "DE", // Germany
    "DK", // Denmark
    "EE", // Estonia
    "ES", // Spain
    "FI", // Finland
    "FR", // France
    "GR", // Greece
    "HR", // Croatia
    "HU", // Hungary
    "IE", // Ireland
    "IT", // Italy
    "LT", // Lithuania
    "LU", // Luxembourg
    "LV", // Latvia
    "MT", // Malta
    "NL", // Netherlands
    "PL", // Poland
    "PT", // Portugal
    "RO", // Romania
    "SE", // Sweden
    "SI", // Slovenia
    "SK", // Slovakia
)

/** Countries for which using EUR is more common than USD. */
val eurCountries = euCountries + arrayOf(
    "GB", // Great Britain
    "UK", // United Kingdom
    "CH", // Switzerland
    "AD", // Andorra
    "MC", // Monaco
    "SM", // San Marino
    "VA", // Vatican City
    "ME", // Montenegro
    "XK", // Kosovo
    "AL", // Albania
    "BA", // Bosnia and Herzegovina
    "MK", // North Macedonia
    "RS", // Serbia
    "TR", // Turkey
)
