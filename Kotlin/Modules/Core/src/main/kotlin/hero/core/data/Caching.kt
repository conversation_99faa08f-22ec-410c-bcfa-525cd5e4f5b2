package hero.core.data

import java.time.Duration

sealed class Caching(val maxAge: Duration) {
    /** https://developers.cloudflare.com/cache/concepts/cache-control/ */
    class Enabled(maxAge: Duration, private val public: Boolean = true) : Caching(maxAge) {
        override val cacheControl: String
            get() = "${if (public) "public, " else ""}must-revalidate, max-age=${maxAge.seconds}"
    }

    data object Disabled : Caching(Duration.ZERO) {
        override val cacheControl: String
            get() = "private, no-store, no-cache, must-revalidate, max-age=0" // somewhat paranoid
    }

    abstract val cacheControl: String
}
